'use client'

import { useMemo, useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, FileText, Home, GraduationCap } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSubjectWithDetails } from '@/hooks/use-education-data';
import { isYearAllowedForExams } from '@/utils/examFilter';
import { encodeIdForUrl } from '@/utils/id-utils';
import EmptyContentMessage, { EmptyTabMessage } from '@/components/EmptyContentMessage';
import type { Lesson } from '@/data/types';

interface SubjectClientProps {
  subjectId: string;
}

export default function SubjectClient({ subjectId }: SubjectClientProps) {
  const { subject, year, lessons, isLoading, isError } = useSubjectWithDetails(subjectId);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('exercises');

  // تحديد التبويب النشط من URL parameters
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['exercises', 'homeworks', 'summaries', 'exams'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // تصنيف الدروس حسب النوع وإدارة الفلتر
  const { exercises, homeworks, summaries, exams, isExamLevelAllowed, visibleTabs, availableTabsCount, hasAnyContent } = useMemo(() => {
    const exercises = lessons.filter(lesson => lesson.content_type === 'exercise');
    const homeworks = lessons.filter(lesson => lesson.content_type === 'homework');
    const summaries = lessons.filter(lesson => lesson.content_type === 'summary');
    const exams = lessons.filter(lesson => lesson.content_type === 'exam');

    // تحديد ما إذا كان هذا المستوى مسموح له بعرض الامتحانات
    const yearId = year?.id || '';
    const isExamLevelAllowed = isYearAllowedForExams(yearId);

    // تحديد التبويبات المرئية (التي تحتوي على محتوى)
    const visibleTabs = {
      exercises: exercises.length > 0,
      homeworks: homeworks.length > 0,
      summaries: summaries.length > 0,
      exams: exams.length > 0 && isExamLevelAllowed
    };

    // حساب عدد التبويبات المتاحة
    const availableTabsCount = Object.values(visibleTabs).filter(Boolean).length;

    // فحص وجود أي محتوى
    const hasAnyContent = availableTabsCount > 0;

    return {
      exercises,
      homeworks,
      summaries,
      exams,
      isExamLevelAllowed,
      visibleTabs,
      availableTabsCount,
      hasAnyContent
    };
  }, [lessons, year]);

  // دالة للحصول على أول تبويب متاح
  const getFirstAvailableTab = () => {
    if (visibleTabs.exercises) return 'exercises';
    if (visibleTabs.homeworks) return 'homeworks';
    if (visibleTabs.summaries) return 'summaries';
    if (visibleTabs.exams) return 'exams';
    return 'exercises'; // fallback
  };

  // دالة لتحديث التبويب النشط وURL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    router.replace(url.pathname + url.search, { scroll: false });
  };

  // تحديث التبويب النشط إذا كان غير متاح
  useEffect(() => {
    const currentTabVisible = visibleTabs[activeTab as keyof typeof visibleTabs];
    if (!currentTabVisible && hasAnyContent) {
      const firstAvailableTab = getFirstAvailableTab();
      setActiveTab(firstAvailableTab);
    }
  }, [visibleTabs, activeTab, hasAnyContent, getFirstAvailableTab]);



  const renderLessonCard = (lesson: Lesson, type: string) => {
    const getIcon = () => {
      switch (type) {
        case 'exercise': return <BookOpen className="h-6 w-6" />;
        case 'homework': return <Home className="h-6 w-6" />;
        case 'summary': return <FileText className="h-6 w-6" />;
        case 'exam': return <GraduationCap className="h-6 w-6" />;
        default: return <BookOpen className="h-6 w-6" />;
      }
    };

    const getRoute = () => {
      const encodedLessonId = encodeIdForUrl(lesson.id);
      const encodedSubjectId = encodeIdForUrl(subjectId);

      const baseRoute = (() => {
        switch (type) {
          case 'exercise': return `/lesson/${encodedLessonId}`;
          case 'homework': return `/homework/${encodedLessonId}`;
          case 'summary': return `/summary/${encodedLessonId}`;
          case 'exam': return `/exam/${encodedLessonId}`;
          default: return `/lesson/${encodedLessonId}`;
        }
      })();

      // إضافة معامل العودة للحفاظ على التبويب النشط
      const returnTab = (() => {
        switch (type) {
          case 'exercise': return 'exercises';
          case 'homework': return 'homeworks';
          case 'summary': return 'summaries';
          case 'exam': return 'exams';
          default: return 'exercises';
        }
      })();

      return `${baseRoute}?returnTo=${encodeURIComponent(`/subject/${encodedSubjectId}?tab=${returnTab}`)}`;
    };

    return (
      <Link
        key={lesson.id}
        href={getRoute()}
        className="level-card transition-all duration-300 hover:shadow-md hover:scale-[1.02]"
        dir="rtl"
      >
        <div className="flex items-center gap-4 rtl-flex-row-reverse">
          <div className="flex-1 text-right flex items-center">
            <div className="text-lg font-bold arabic-heading">{lesson.title}</div>
          </div>
          <div className="p-2 bg-primary/10 rounded-lg text-primary">
            {getIcon()}
          </div>
        </div>
        <div className="bg-muted rounded-full p-2 transition-all duration-300 hover:bg-primary/20">
          <ArrowLeft className="h-5 w-5 text-primary transition-transform hover:translate-x-1" />
        </div>
      </Link>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg arabic-text">جاري تحميل البيانات...</p>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  if (isError || !subject) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <h1 className="text-2xl font-bold text-destructive mb-4 arabic-heading">خطأ</h1>
            <p className="text-lg mb-4 arabic-text">حدث خطأ أثناء تحميل البيانات أو لم يتم العثور على المادة الدراسية</p>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors rtl-button"
              dir="rtl"
            >
              العودة إلى المستويات
              <ArrowLeft className="h-4 w-4 ml-2 rtl:rotate-180" />
            </Link>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12" dir="rtl">
          <div className="mb-4">
            <h1 className="text-3xl md:text-4xl font-bold text-primary text-center">{subject.name}</h1>
          </div>
          {subject.description && (
            <div className="mb-4">
              <p className="text-lg text-foreground text-center ">
                {subject.description}
              </p>
            </div>
          )}
          
        </div>

        {/* Breadcrumb */}
        <div className="mb-8" dir="rtl">
          <nav className="rtl-breadcrumb">
            <Link href="/levels" className="hover:text-primary transition-colors arabic-text">
              المستويات
            </Link>
            <span className="rtl-breadcrumb-separator">/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors arabic-text">
                  {year.name}
                </Link>
                <span className="rtl-breadcrumb-separator">/</span>
              </>
            )}
            <span className="text-foreground arabic-text">{subject.name}</span>
          </nav>
        </div>

        {/* Navigation Guide */}
        <div className="mb-8 p-6 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl border border-primary/10" dir="rtl">
          <h2 className="text-xl font-bold text-primary mb-3 arabic-heading">محتويات المادة</h2>
          <p className="text-muted-foreground mb-4 arabic-text">
            يمكنك التنقل بين أقسام المادة المختلفة باستخدام التبويبات أدناه:
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2 text-primary">
              <BookOpen className="h-4 w-4" />
              <span>التمارين: للممارسة والتطبيق</span>
            </div>
            <div className="flex items-center gap-2 text-primary">
              <Home className="h-4 w-4" />
              <span>الفروض: للتقييم المنزلي</span>
            </div>
            <div className="flex items-center gap-2 text-primary">
              <FileText className="h-4 w-4" />
              <span>الملخصات: للمراجعة السريعة</span>
            </div>
            {isExamLevelAllowed && (
              <div className="flex items-center gap-2 text-primary">
                <GraduationCap className="h-4 w-4" />
                <span>الامتحانات: للتقييم النهائي</span>
              </div>
            )}
          </div>
        </div>

        {/* Content Tabs - عرض التبويبات فقط إذا كان هناك محتوى متاح */}
        {hasAnyContent ? (
          <div className="w-full max-w-4xl mx-auto">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <div className="overflow-hidden rounded-xl bg-white border border-gray-200 shadow-lg">
                <TabsList className={`grid w-full ${
                  // للشاشات الصغيرة: عمودين فقط، للشاشات الكبيرة: حسب العدد
                  availableTabsCount === 1 ? 'grid-cols-1' :
                  availableTabsCount === 2 ? 'grid-cols-2' :
                  availableTabsCount === 3 ? 'grid-cols-2 md:grid-cols-3' :
                  'grid-cols-2 md:grid-cols-4'
                } h-auto p-2 bg-transparent gap-2`}>
              {visibleTabs.exercises && (
                <TabsTrigger
                  value="exercises"
                  className="flex flex-col items-center justify-center gap-1 px-1 py-2 text-xs md:text-sm font-medium rounded-lg transition-all duration-300 bg-blue-50 border border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 hover:shadow-md data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:border-blue-600 data-[state=active]:shadow-lg min-h-[80px] md:min-h-[90px]"
                >
                  <BookOpen className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                  <span className="font-semibold text-center leading-tight text-xs md:text-sm">التمارين</span>
                  <span className="bg-white/20 text-current px-1.5 py-0.5 rounded-full text-xs font-bold data-[state=active]:bg-white/30 flex-shrink-0">
                    {exercises.length}
                  </span>
                </TabsTrigger>
              )}
              {visibleTabs.homeworks && (
                <TabsTrigger
                  value="homeworks"
                  className="flex flex-col items-center justify-center gap-1 px-1 py-2 text-xs md:text-sm font-medium rounded-lg transition-all duration-300 bg-orange-50 border border-orange-200 text-orange-700 hover:bg-orange-100 hover:border-orange-300 hover:shadow-md data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:border-orange-600 data-[state=active]:shadow-lg min-h-[80px] md:min-h-[90px]"
                >
                  <Home className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                  <span className="font-semibold text-center leading-tight text-xs md:text-sm">الفروض</span>
                  <span className="bg-white/20 text-current px-1.5 py-0.5 rounded-full text-xs font-bold data-[state=active]:bg-white/30 flex-shrink-0">
                    {homeworks.length}
                  </span>
                </TabsTrigger>
              )}
              {visibleTabs.summaries && (
                <TabsTrigger
                  value="summaries"
                  className="flex flex-col items-center justify-center gap-1 px-1 py-2 text-xs md:text-sm font-medium rounded-lg transition-all duration-300 bg-green-50 border border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 hover:shadow-md data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=active]:shadow-lg min-h-[80px] md:min-h-[90px]"
                >
                  <FileText className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                  <span className="font-semibold text-center leading-tight text-xs md:text-sm">الملخصات</span>
                  <span className="bg-white/20 text-current px-1.5 py-0.5 rounded-full text-xs font-bold data-[state=active]:bg-white/30 flex-shrink-0">
                    {summaries.length}
                  </span>
                </TabsTrigger>
              )}
              {visibleTabs.exams && (
                <TabsTrigger
                  value="exams"
                  className="flex flex-col items-center justify-center gap-1 px-2 py-3 text-xs md:text-sm font-medium rounded-lg transition-all duration-300 bg-purple-50 border border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 hover:shadow-md data-[state=active]:bg-purple-500 data-[state=active]:text-white data-[state=active]:border-purple-600 data-[state=active]:shadow-lg transform hover:scale-105 data-[state=active]:scale-105"
                >
                  <GraduationCap className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="font-semibold text-center leading-tight">الامتحانات</span>
                  <span className="bg-white/20 text-current px-1.5 py-0.5 rounded-full text-xs font-bold data-[state=active]:bg-white/30">
                    {exams.length}
                  </span>
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="exercises" className="mt-8">
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg" dir="rtl">
              <div className="flex items-center gap-2 mb-2">
                <BookOpen className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-blue-800 arabic-heading">قسم التمارين</h3>
              </div>
              <p className="text-blue-700 text-sm arabic-text">
                هنا ستجد التمارين التطبيقية لتعزيز فهمك للمادة وتطوير مهاراتك العملية
              </p>
            </div>
            {exercises.length > 0 ? (
              <div className="space-y-6">
                {exercises.map(lesson => renderLessonCard(lesson, 'exercise'))}
              </div>
            ) : (
              <EmptyTabMessage type="exercises" />
            )}
          </TabsContent>

          <TabsContent value="homeworks" className="mt-8">
            <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg" dir="rtl">
              <div className="flex items-center gap-2 mb-2">
                <Home className="h-5 w-5 text-orange-600" />
                <h3 className="font-semibold text-orange-800 arabic-heading">قسم الفروض</h3>
              </div>
              <p className="text-orange-700 text-sm arabic-text">
                الفروض المنزلية والواجبات التي تساعدك على تطبيق ما تعلمته وتقييم مستواك
              </p>
            </div>
            {homeworks.length > 0 ? (
              <div className="space-y-6">
                {homeworks.map(lesson => renderLessonCard(lesson, 'homework'))}
              </div>
            ) : (
              <EmptyTabMessage type="homeworks" />
            )}
          </TabsContent>

          <TabsContent value="summaries" className="mt-8">
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg" dir="rtl">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold text-green-800 arabic-heading">قسم الملخصات</h3>
              </div>
              <p className="text-green-700 text-sm arabic-text">
                ملخصات شاملة للدروس تساعدك على المراجعة السريعة والفعالة قبل الامتحانات
              </p>
            </div>
            {summaries.length > 0 ? (
              <div className="space-y-6">
                {summaries.map(lesson => renderLessonCard(lesson, 'summary'))}
              </div>
            ) : (
              <EmptyTabMessage type="summaries" />
            )}
          </TabsContent>

          {isExamLevelAllowed && (
            <TabsContent value="exams" className="mt-8">
              <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg" dir="rtl">
                <div className="flex items-center gap-2 mb-2">
                  <GraduationCap className="h-5 w-5 text-purple-600" />
                  <h3 className="font-semibold text-purple-800 arabic-heading">قسم الامتحانات</h3>
                </div>
                <p className="text-purple-700 text-sm arabic-text">
                  امتحانات تقييمية شاملة لقياس مستوى فهمك وإتقانك للمادة الدراسية
                </p>
              </div>
              {exams.length > 0 ? (
                <div className="space-y-6">
                  {exams.map(lesson => renderLessonCard(lesson, 'exam'))}
                </div>
              ) : (
                <EmptyTabMessage type="exams" />
              )}
            </TabsContent>
          )}
              </div>
            </Tabs>
          </div>
        ) : (
          // عرض رسالة عندما لا يوجد محتوى متاح
          <EmptyContentMessage type="general" />
        )}
      </div>

      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
}
