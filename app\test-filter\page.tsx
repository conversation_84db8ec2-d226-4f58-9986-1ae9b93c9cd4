'use client'

import { useState } from 'react';
import { BookOpen, Home, FileText, GraduationCap } from 'lucide-react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import EmptyContentMessage, { EmptyTabMessage } from '@/components/EmptyContentMessage';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

// بيانات تجريبية لاختبار الفلتر
const mockLessons = [
  { id: '1', title: 'تمرين 1', content_type: 'exercise' as const },
  { id: '2', title: 'تمرين 2', content_type: 'exercise' as const },
  { id: '3', title: 'فرض 1', content_type: 'homework' as const },
  // { id: '4', title: 'ملخص 1', content_type: 'summary' as const },
  // { id: '5', title: 'امتحان 1', content_type: 'exam' as const },
];

export default function TestFilterPage() {
  const [lessons, setLessons] = useState(mockLessons);
  const [activeTab, setActiveTab] = useState('exercises');

  // تصنيف الدروس حسب النوع وإدارة الفلتر
  const { exercises, homeworks, summaries, exams, visibleTabs, hasAnyContent } = (() => {
    const exercises = lessons.filter(lesson => lesson.content_type === 'exercise');
    const homeworks = lessons.filter(lesson => lesson.content_type === 'homework');
    const summaries = lessons.filter(lesson => lesson.content_type === 'summary');
    const exams = lessons.filter(lesson => lesson.content_type === 'exam');

    // تحديد التبويبات المرئية (التي تحتوي على محتوى)
    const visibleTabs = {
      exercises: exercises.length > 0,
      homeworks: homeworks.length > 0,
      summaries: summaries.length > 0,
      exams: exams.length > 0
    };

    // حساب عدد التبويبات المتاحة
    const availableTabsCount = Object.values(visibleTabs).filter(Boolean).length;
    
    // فحص وجود أي محتوى
    const hasAnyContent = availableTabsCount > 0;

    return {
      exercises,
      homeworks,
      summaries,
      exams,
      visibleTabs,
      hasAnyContent
    };
  })();

  // دالة للحصول على أول تبويب متاح
  const getFirstAvailableTab = () => {
    if (visibleTabs.exercises) return 'exercises';
    if (visibleTabs.homeworks) return 'homeworks';
    if (visibleTabs.summaries) return 'summaries';
    if (visibleTabs.exams) return 'exams';
    return 'exercises';
  };

  // دوال لإضافة وحذف المحتوى للاختبار
  const addExercise = () => {
    const newExercise = {
      id: `ex_${Date.now()}`,
      title: `تمرين ${exercises.length + 1}`,
      content_type: 'exercise' as const
    };
    setLessons([...lessons, newExercise]);
  };

  const addHomework = () => {
    const newHomework = {
      id: `hw_${Date.now()}`,
      title: `فرض ${homeworks.length + 1}`,
      content_type: 'homework' as const
    };
    setLessons([...lessons, newHomework]);
  };

  const addSummary = () => {
    const newSummary = {
      id: `sum_${Date.now()}`,
      title: `ملخص ${summaries.length + 1}`,
      content_type: 'summary' as const
    };
    setLessons([...lessons, newSummary]);
  };

  const addExam = () => {
    const newExam = {
      id: `exam_${Date.now()}`,
      title: `امتحان ${exams.length + 1}`,
      content_type: 'exam' as const
    };
    setLessons([...lessons, newExam]);
  };

  const clearAll = () => {
    setLessons([]);
  };

  const resetToDefault = () => {
    setLessons(mockLessons);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />
      
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8" dir="rtl">
            <h1 className="text-4xl font-bold mb-4 arabic-heading">
              اختبار نظام فلتر المحتوى
            </h1>
            <p className="text-lg text-muted-foreground arabic-text">
              اختبر كيفية عمل الفلتر الجديد لإخفاء وإظهار التبويبات
            </p>
          </div>

          {/* أزرار التحكم */}
          <div className="mb-8 p-6 bg-white rounded-xl shadow-sm" dir="rtl">
            <h3 className="text-lg font-semibold mb-4 arabic-heading">أزرار التحكم</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <button
                onClick={addExercise}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                إضافة تمرين
              </button>
              <button
                onClick={addHomework}
                className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              >
                إضافة فرض
              </button>
              <button
                onClick={addSummary}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                إضافة ملخص
              </button>
              <button
                onClick={addExam}
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
              >
                إضافة امتحان
              </button>
            </div>
            <div className="flex gap-4">
              <button
                onClick={clearAll}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                حذف الكل
              </button>
              <button
                onClick={resetToDefault}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إعادة تعيين
              </button>
            </div>
          </div>

          {/* عرض الإحصائيات */}
          <div className="mb-8 p-6 bg-white rounded-xl shadow-sm" dir="rtl">
            <h3 className="text-lg font-semibold mb-4 arabic-heading">الإحصائيات الحالية</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{exercises.length}</div>
                <div className="text-sm text-gray-600">تمارين</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{homeworks.length}</div>
                <div className="text-sm text-gray-600">فروض</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summaries.length}</div>
                <div className="text-sm text-gray-600">ملخصات</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{exams.length}</div>
                <div className="text-sm text-gray-600">امتحانات</div>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            {hasAnyContent ? (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-4 h-auto p-2 bg-muted/50 rounded-xl shadow-sm">
                  {visibleTabs.exercises && (
                    <TabsTrigger value="exercises" className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      <span>التمارين ({exercises.length})</span>
                    </TabsTrigger>
                  )}
                  {visibleTabs.homeworks && (
                    <TabsTrigger value="homeworks" className="flex items-center gap-2">
                      <Home className="h-4 w-4" />
                      <span>الفروض ({homeworks.length})</span>
                    </TabsTrigger>
                  )}
                  {visibleTabs.summaries && (
                    <TabsTrigger value="summaries" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>الملخصات ({summaries.length})</span>
                    </TabsTrigger>
                  )}
                  {visibleTabs.exams && (
                    <TabsTrigger value="exams" className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4" />
                      <span>الامتحانات ({exams.length})</span>
                    </TabsTrigger>
                  )}
                </TabsList>

                <TabsContent value="exercises" className="mt-8">
                  {exercises.length > 0 ? (
                    <div className="space-y-4">
                      {exercises.map(exercise => (
                        <div key={exercise.id} className="p-4 border rounded-lg">
                          <h4 className="font-semibold">{exercise.title}</h4>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <EmptyTabMessage type="exercises" />
                  )}
                </TabsContent>

                <TabsContent value="homeworks" className="mt-8">
                  {homeworks.length > 0 ? (
                    <div className="space-y-4">
                      {homeworks.map(homework => (
                        <div key={homework.id} className="p-4 border rounded-lg">
                          <h4 className="font-semibold">{homework.title}</h4>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <EmptyTabMessage type="homeworks" />
                  )}
                </TabsContent>

                <TabsContent value="summaries" className="mt-8">
                  {summaries.length > 0 ? (
                    <div className="space-y-4">
                      {summaries.map(summary => (
                        <div key={summary.id} className="p-4 border rounded-lg">
                          <h4 className="font-semibold">{summary.title}</h4>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <EmptyTabMessage type="summaries" />
                  )}
                </TabsContent>

                <TabsContent value="exams" className="mt-8">
                  {exams.length > 0 ? (
                    <div className="space-y-4">
                      {exams.map(exam => (
                        <div key={exam.id} className="p-4 border rounded-lg">
                          <h4 className="font-semibold">{exam.title}</h4>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <EmptyTabMessage type="exams" />
                  )}
                </TabsContent>
              </Tabs>
            ) : (
              <EmptyContentMessage type="general" />
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
